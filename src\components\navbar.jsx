import { Link } from "react-router-dom";
import { useState } from "react";
import "/src/App.css";
const dropdown_menu = [
  { name: "Action", link: "/" },
  { name: "Adventure", link: "/" },
  { name: "Sports", link: "/" },
  { name: "RPG", link: "/" },
  { name: "FPS", link: "/" },
  { name: "Racing", link: "/" },
  { name: "Open World", link: "/" },
];

const footerLinks = [
  {
    title: "Footer Content",
    content:
      "A site created to increase and improve piracy of games. Enjoy your adventure into the world of pirates!",
  },
  {
    title: "Quick Links",
    links: ["Home", "Categories", "Contact"],
  },
  {
    title: "More Links",
    links: ["Link A", "Link B", "Link C", "Link D"],
  },
];

function Dropdown() {
  return (
    <div>
      {dropdown_menu.map((item, index) => {
        return (
          <Link key={index} to={item.link} className="dropdown-link">
            {item.name}
          </Link>
        );
      })}
    </div>
  );
}

function Navbar() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  return (
    <div
      className="navbar"
      style={{
        height: "55px",
        fontFamily: "Verdana, Geneva, Tahoma, sans-serif",
        backgroundColor: "rgb(10, 10, 10)",
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        padding: "0 20px",
      }}
    >
      {/* Logo - Always visible */}
      <div className="logo" style={{ display: "block", paddingRight: "20px" }}>
        <img
          src="https://logosandtypes.com/wp-content/uploads/2020/08/xbox-old.svg"
          height={50}
          width={50}
          alt="logo"
        />
      </div>

      {/* Mobile hamburger button */}
      <button
        className="mobile-menu-toggle"
        onClick={toggleMobileMenu}
        aria-label="Toggle mobile menu"
      >
        ☰
      </button>

      {/* Desktop Layout & Mobile Menu Content */}
      <div className={`navbar-content ${isMobileMenuOpen ? 'active' : ''}`}>
        <div
          className="links"
          style={{
            display: "flex",
            gap: "20px",
            color: "white",
            textDecoration: "none",
            fontWeight: "500",
            flex: 1,
          }}
        >
          <Link to="/" onClick={closeMobileMenu}>Home</Link>
          <div className="dropdown">
            <Link to="/" onClick={closeMobileMenu}>Categories</Link>
            <Dropdown />
          </div>
          <Link to="/" onClick={closeMobileMenu}>Contact</Link>
        </div>

        <div
          className="search-bar"
          style={{
            display: "flex",
            flex: 1,
          }}
        >
          <input type="search" placeholder="Search" />
        </div>

        <div
          className="right-navbar"
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "flex-end",
            padding: "0 20px",
            gap: "10px",
          }}
        >
          <div className="login-div">
            <Link to="/login" onClick={closeMobileMenu}>
            <button className="login-button">LOG IN</button>
            </Link>
          </div>
          <div className="signin-div">
          <Link to="/signin" onClick={closeMobileMenu}>
            <button className="signin-button">SIGN IN</button>
          </Link>
          </div>
        </div>
      </div>
    </div>
  );
}

export function Footer() {
  return (
    <footer style={{ color: "#f1f1f1", backgroundColor:"black" }}>
      <div>
        <div
          className="footer-content"
          style={{
            display: "flex",
            flexWrap: "wrap",
            justifyContent: "center",
            backgroundColor: "#000",
            borderRadius: "10px",
          }}
        >
          {footerLinks.map((section, index) => (
            <div key={index} className="footer-section" style={{ flex: "1 1 30%", padding: "10px" }}>
              <h5 style={{ textTransform: "uppercase" }}>{section.title}</h5>
              {section.content ? (
                <p>{section.content}</p>
              ) : (
                <ul style={{ listStyle: "none", padding: 0 }}>
                  {section.links.map((link, index) => (
                    <li key={index}>
                      <a
                        href="/"
                        style={{ color: "#f1f1f1", textDecoration: "none" }}
                      >
                        {link}
                      </a>
                    </li>
                  ))}
                </ul>
              )}
            </div>
          ))}
        </div>
      </div>

      <div
        style={{
          textAlign: "center",
          padding: "10px",
          backgroundColor: "#000",
        }}
      >
        © 2026 Copyright
      </div>
    </footer>
  );
}

export { Dropdown , Navbar};
