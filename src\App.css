.app {
  background-color: rgb(0, 0, 0);
  height: 194vmax;
}

.logo img {
  display: block;
  padding-right: 20px;
}

.links {
  display: flex;
  gap: 20px;
}

.links a {
  text-decoration: none;
  color: white;
  font-weight: 500;
}

.link a:hover{
  text-decoration: wavy;
}

.dropdown {
  position: relative;
}

.dropdown div {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  background-color: #f1f1f1;
  padding: 5px;
  border-radius: 17px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
  z-index: 10;
}

.dropdown div a {
  display: block;
  padding: 5px 15px;
  color: #555;
  border-radius: 15px;
  text-decoration: none;
  white-space: nowrap;
}

.dropdown div a:hover {
  background-color: #d2d2d2;
  border-radius: 25px;
  border-color: #333;
}
.dropdown:hover div {
  display: block;
}

.dropdown-link {
  background-color: #f1f1f1;
  border-radius: 15px;
  display: block;
  text-decoration: none;
  color: white;
  transition: background-color 0.5s ease-in-out;
}

.dropdown-link:hover {
  background-color: rgb(30, 30, 30);
}
.search-bar img {
  transition: all 0.3s ease-in-out;
}

.search-bar input {
  color: #f1f1f1;
  width: 200px;
  height: 30px;
  margin: 0 10px;
  font-size: 16px;
  border-radius: 20px;
  background-color: #333;
  transition: all 0.3s ease-in-out;
  padding: 10px;
}

.search-bar input:focus {
  width: 250px;
  background-color: #222;
  border: 1px solid rgb(10,10,10);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

.search-bar input:hover {
  border: 1px solid lightblue;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

.login-button {
  height: 30px;
  width: 90px;
  background-color: transparent;
  color: white;
  border-color: white;
  border-radius: 20px;
  text-align: center;
  font-size: 16px;
  font-weight: 650;
  transition: background-color 0.3s ease-in-out;
  text-transform: uppercase;
}
.login-button:hover {
  background-color: white;
  color: black;
}

.signin-button {
  height: 30px;
  width: 90px;
  background-color: transparent;
  color: white;
  border-color: white;
  border-radius: 20px;
  text-align: center;
  font-size: 16px;
  font-weight: 650;
  transition: background-color 0.3s ease-in-out;
  text-transform: uppercase;
}

.signin-button:hover {
  background-color: white;
  color: black;
}

.loading .app {
  opacity: 0;
  animation: reveal 2s forwards;
}

.loading .navbar {
  opacity: 0;
  animation: reveal 0.5s forwards;
  animation-delay: 0.5s;
}

.loading .games {
  opacity: 0;
  animation: reveal 0.5s forwards;
  animation-delay: 1s;
}

.loading .footer {
  opacity: 0;
  animation: reveal 0.5s forwards;
  animation-delay: 1.5s;
}

/* Games.css*/

/* Animation for {category.genre} Games */
.genre-heading::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 0;
  height: 2px;
  background-color: white;
  transition: width 0.25s ease;
}
.genre-heading:hover::after {
  width: 100%;
}

.see-all-link::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: -2px;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #00d4ff, #ffffff);
  transition: width 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border-radius: 1px;
}

.see-all-link:hover::after {
  width: 75%;
}

.see-all-link:hover {
  color: #00d4ff;
  transition: color 0.3s ease;
}

.game-link {
  position: relative;
  transition: color 0.3s ease;
  display: inline-block;
  overflow: hidden;
}

.game-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #00d4ff, #0099cc);
  transition: width 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border-radius: 1px;
}

.game-link:hover::after {
  width: 100%;
}

.game-link:hover {
  color: #00d4ff;
  transform: translateY(-1px);
}

/* Enhanced game card hover effects */
.game-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
}

.game-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 212, 255, 0.15) !important;
}

.game-card:hover img {
  transform: scale(1.02);
  transition: transform 0.3s ease;
}

.game-card img {
  transition: transform 0.3s ease;
}

.game-link:focus {
  outline: 2px solid #00d4ff;
  outline-offset: 2px;
  border-radius: 2px;
}

.game-link:focus::after {
  width: 100%;
}

/* General responsive utilities */
.container {
  max-width: 100%;
  padding: 0 15px;
  margin: 0 auto;
}

/* Hide elements on mobile */
.mobile-hidden {
  display: none;
}

/* Show elements only on mobile */
.mobile-only {
  display: block;
}

/* Smooth scrolling for the entire page */
html {
  scroll-behavior: smooth;
}

/* Sticky navbar with beautiful blur effect */
.navbar {
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(15px) saturate(100%);
  -webkit-backdrop-filter: blur(15px) saturate(100%);
  background-color: rgba(10, 10, 10, 0.85) !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

/* Enhanced blur on hover */
.navbar:hover {
  backdrop-filter: blur(20px) saturate(200%);
  -webkit-backdrop-filter: blur(20px) saturate(200%);
  border-bottom: 1px solid rgba(0, 212, 255, 0.2);
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.2);
}

/* Fallback for browsers that don't support backdrop-filter */
@supports not (backdrop-filter: blur(15px)) {
  .navbar {
    background-color: rgba(10, 10, 10, 0.95) !important;
  }

  .navbar:hover {
    background-color: rgba(10, 10, 10, 0.98) !important;
  }
}

/* Enhanced blur animation */
@keyframes blurIn {
  from {
    backdrop-filter: blur(0px) saturate(100%);
    -webkit-backdrop-filter: blur(0px) saturate(100%);
  }
  to {
    backdrop-filter: blur(15px) saturate(100%);
    -webkit-backdrop-filter: blur(15px) saturate(100%);
  }
}

/* Ensure sidebar links work with smooth scrolling */
.sidebar a[href^="#"] {
  scroll-margin-top: 80px;
}

/* Games container to handle sidebar positioning */
.games {
  position: relative;
}

/* Mobile First Approach - Base styles for mobile */
@media (max-width: 768px) {
  /* App Layout */
  .app {
    height: auto;
    min-height: 100vh;
  }

  /* Navbar Responsive */
  .navbar {
    flex-direction: column !important;
    height: auto !important;
    padding: 10px !important;
    position: sticky !important;
    top: 0 !important;
    z-index: 100 !important;
    backdrop-filter: blur(12px) saturate(150%) !important;
    -webkit-backdrop-filter: blur(12px) saturate(150%) !important;
    background-color: rgba(10, 10, 10, 0.9) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.2) !important;
  }

  .logo {
    padding-right: 10px !important;
  }

  .logo img {
    height: 40px !important;
    width: 40px !important;
  }

  .mobile-menu-toggle {
    display: block;
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 5px;
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
  }

  .navbar-content {
    display: none;
    width: 100%;
    flex-direction: column;
    gap: 15px;
  }

  .navbar-content.active {
    display: flex;
  }

  .links {
    flex-direction: column !important;
    gap: 15px !important;
    width: 100%;
    text-align: center;
  }

  .dropdown {
    position: static;
  }

  .dropdown div {
    position: static !important;
    display: block !important;
    background-color: #333 !important;
    margin-top: 10px;
    border-radius: 5px;
    box-shadow: none;
  }

  .search-bar {
    width: 100% !important;
    justify-content: center;
  }

  .search-bar input {
    width: 100% !important;
    max-width: 300px;
    margin: 0 !important;
  }

  .search-bar input:focus {
    width: 100% !important;
  }

  .right-navbar {
    justify-content: center !important;
    padding: 0 !important;
    gap: 15px !important;
  }

  .login-button, .signin-button {
    width: 100px !important;
    font-size: 14px !important;
  }

  /* Games Layout */
  .games {
    display: block !important;
    grid-template-columns: none !important;
  }

  .sidebar {
    position: static !important;
    height: auto !important;
    margin-bottom: 20px;
    padding: 15px !important;
  }

  .sidebar h2 {
    font-size: 18px;
    margin: 0 0 15px 0 !important;
  }

  .sidebar .list-group-item {
    padding: 8px 12px !important;
    font-size: 14px;
  }

  /* Game Cards */
  .games-content {
    padding: 10px !important;
  }

  .genre-section {
    margin-bottom: 30px !important;
    padding: 15px !important;
  }

  .genre-heading {
    font-size: 20px !important;
  }

  .games-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)) !important;
    gap: 15px !important;
    justify-content: center;
  }

  .game-card {
    width: 100% !important;
    height: auto !important;
    min-height: 220px;
    padding: 10px !important;
  }

  .game-card img {
    width: 100% !important;
    height: 150px !important;
    object-fit: cover;
  }

  .game-link {
    font-size: 14px !important;
    margin: 10px 0 0 0 !important;
  }

  /* Footer */
  .footer-content {
    flex-direction: column !important;
    text-align: center;
  }

  .footer-section {
    flex: none !important;
    margin-bottom: 20px;
  }

  .footer-section h5 {
    font-size: 16px;
  }

  .footer-section p, .footer-section li {
    font-size: 14px;
  }
}

/* Tablet Styles */
@media (min-width: 769px) and (max-width: 1024px) {
  .navbar {
    padding: 0 15px !important;
    position: relative !important;
  }

  .mobile-menu-toggle {
    display: none !important;
  }

  .navbar-content {
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    width: 100% !important;
    position: static !important;
  }

  .search-bar input {
    width: 180px !important;
  }

  .search-bar input:focus {
    width: 220px !important;
  }

  .games {
    grid-template-columns: 20% 1fr !important;
  }

  .sidebar {
    padding: 15px !important;
    position: sticky !important;
    top: 55px !important; /* Stick right below navbar */
    height: calc(100vh - 55px) !important;
    overflow-y: auto !important;
    z-index: 10;
    margin-top: 0 !important;
    border-top: 1px solid rgba(0, 212, 255, 0.1);
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  }

  /* Tablet scrollbar styling */
  .sidebar::-webkit-scrollbar {
    width: 4px;
  }

  .sidebar::-webkit-scrollbar-track {
    background: #2a2a2a;
    border-radius: 2px;
  }

  .sidebar::-webkit-scrollbar-thumb {
    background: #555;
    border-radius: 2px;
  }

  .games-grid {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 18px !important;
  }

  .game-card {
    width: 180px !important;
    height: 250px !important;
  }

  .game-card img {
    width: 130px !important;
    height: 180px !important;
  }
}

/* Desktop Styles - Keep existing layout but add some improvements */
@media (min-width: 1025px) {
  .mobile-menu-toggle {
    display: none !important;
  }

  .mobile-hidden {
    display: block;
  }

  .mobile-only {
    display: none;
  }

  .navbar {
    position: relative !important;
  }

  .navbar-content {
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    width: 100% !important;
    position: static !important;
  }

  .games-grid {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 23px !important;
    justify-content: flex-start;
  }

  .container {
    max-width: 1200px;
    padding: 0 20px;
  }

  /* Sticky sidebar for desktop */
  .sidebar {
    position: sticky !important;
    top: 55px !important; /* Stick right below navbar */
    height: calc(100vh - 55px) !important;
    overflow-y: auto !important;
    z-index: 10;
    margin-top: 0 !important;
    border-top: 1px solid rgba(0, 212, 255, 0.1);
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  }

  /* Smooth scrolling for sidebar */
  .sidebar::-webkit-scrollbar {
    width: 6px;
  }

  .sidebar::-webkit-scrollbar-track {
    background: #2a2a2a;
    border-radius: 3px;
  }

  .sidebar::-webkit-scrollbar-thumb {
    background: #555;
    border-radius: 3px;
  }

  .sidebar::-webkit-scrollbar-thumb:hover {
    background: #777;
  }

  /* Active genre highlighting */
  .sidebar .list-group-item:hover {
    background-color: rgba(0, 212, 255, 0.1);
    border-radius: 5px;
    transition: background-color 0.3s ease;
  }

  .sidebar .list-group-item a:hover {
    color: #00d4ff !important;
    transition: color 0.3s ease;
  }
}

/* Large Desktop Styles */
@media (min-width: 1440px) {
  .container {
    max-width: 1400px;
    padding: 0 30px;
  }

  .games-grid {
    gap: 30px !important;
  }

  .game-card {
    width: 220px !important;
    height: 290px !important;
  }

  .game-card img {
    width: 170px !important;
    height: 220px !important;
  }
}

/* Details.css */

.buynow {
  background-color: transparent;
  color: white;
  border-color: white;
  padding: 15px 32px;
  text-align: center;
  font-size: 16px;
  margin: 4px 2px;
  cursor: pointer;
  border-radius: 12px;
  transition: background-color 0.3s ease-in-out;
  height: 70px;
  width: 200px;
  text-transform: uppercase;
}

.buynow:hover {
  background-color: white;
  color: black;
}
.details .cover-art:hover {
  animation: hover 0.35s forwards;
}

.details .cover-art {
  animation: hoverReverse 0.7s forwards;
}

.loading .details {
  opacity: 0;
  animation: reveal 1s forwards;
}

.loading .details {
  opacity: 0;
  animation: reveal 0.5s forwards;
  animation-delay: 0.5s;
}

.loading .game-info {
  opacity: 0;
  animation: reveal 0.5s forwards;
  animation-delay: 1s;
}

.loading .trailer {
  opacity: 0;
  animation: reveal 0.5s forwards;
  animation-delay: 1.5s;
}

@keyframes reveal {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes hover {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.05);
  }
}

@keyframes hoverReverse {
  0% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* ===== RESPONSIVE STYLES FOR DETAILS PAGE ===== */

@media (max-width: 768px) {
  .details-container {
    padding: 10px !important;
    max-width: 100% !important;
  }

  .details-content {
    flex-direction: column !important;
    gap: 20px !important;
  }

  .details-left {
    width: 100% !important;
    text-align: center;
  }

  .details-right {
    width: 100% !important;
  }

  .cover-art {
    width: 100% !important;
    max-width: 300px !important;
    height: auto !important;
  }

  .game-title {
    font-size: 24px !important;
    margin: 15px 0 !important;
  }

  .game-developer {
    font-size: 16px !important;
    margin-bottom: 15px !important;
  }

  .game-description {
    font-size: 14px !important;
    line-height: 1.6 !important;
    margin-bottom: 20px !important;
  }

  .trailer-container {
    width: 100% !important;
    margin: 20px 0 !important;
  }

  .trailer-iframe {
    width: 100% !important;
    height: 200px !important;
  }

  .buynow {
    width: 100% !important;
    max-width: 250px !important;
    height: 50px !important;
    font-size: 14px !important;
    padding: 10px 20px !important;
  }

  .action-buttons {
    flex-direction: column !important;
    gap: 15px !important;
    align-items: center;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .details-container {
    padding: 15px !important;
  }

  .details-content {
    gap: 30px !important;
  }

  .cover-art {
    width: 250px !important;
    height: 350px !important;
  }

  .game-title {
    font-size: 28px !important;
  }

  .trailer-iframe {
    height: 250px !important;
  }
}

/* ===== TOUCH AND INTERACTION IMPROVEMENTS ===== */

/* Improve touch targets for mobile */
@media (max-width: 768px) {
  .game-card {
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .game-card:hover,
  .game-card:active {
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0, 212, 255, 0.2) !important;
  }

  .game-card:active img {
    transform: scale(1.01);
  }

  /* Mobile-specific link animations */
  .game-link::after {
    height: 1.5px;
  }

  .game-link:hover {
    transform: translateY(-0.5px);
  }

  .login-button, .signin-button {
    min-height: 44px !important;
    min-width: 44px !important;
  }

  .mobile-menu-toggle {
    min-height: 44px !important;
    min-width: 44px !important;
  }

  /* Improve dropdown for touch */
  .dropdown:hover div,
  .dropdown:focus div,
  .dropdown:active div {
    display: block !important;
  }

  /* Better spacing for touch */
  .sidebar .list-group-item {
    min-height: 44px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* ===== ADDITIONAL RESPONSIVE FIXES ===== */

/* Very small screens (320px and below) */
@media (max-width: 320px) {
  .navbar {
    padding: 5px !important;
  }

  .logo img {
    height: 35px !important;
    width: 35px !important;
  }

  .games-grid {
    grid-template-columns: 1fr !important;
    gap: 10px !important;
  }

  .game-card {
    min-height: 200px !important;
  }

  .game-card img {
    height: 130px !important;
  }

  .genre-heading {
    font-size: 18px !important;
  }

  .sidebar {
    padding: 10px !important;
  }
}

/* Landscape orientation on mobile */
@media (max-width: 768px) and (orientation: landscape) {
  .navbar {
    height: auto !important;
    min-height: 50px;
  }

  .games-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)) !important;
  }

  .game-card {
    min-height: 180px !important;
  }

  .game-card img {
    height: 120px !important;
  }
}

/* Print styles */
@media print {
  .navbar,
  .footer,
  .mobile-menu-toggle,
  .search-bar,
  .login-button,
  .signin-button {
    display: none !important;
  }

  .games {
    display: block !important;
  }

  .sidebar {
    display: none !important;
  }

  .game-card {
    break-inside: avoid;
    page-break-inside: avoid;
  }
}
