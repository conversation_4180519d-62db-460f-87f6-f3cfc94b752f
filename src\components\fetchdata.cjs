const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');

const app = express();
const PORT = 5000;

app.use(cors());
app.use(express.json());

const uri = 'mongodb://localhost:27017/games';
mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true }).then(() => console.log('Connected to MongoDB')).catch((err) => console.error('MongoDB connection error:', err));

const gameSchema = new mongoose.Schema({
  genre: String,
  titles: [
    {
      name: String,
      img: String,
      developers: String,
      description: String,
      trailer: String,
      download: String,
    },
  ],
});

const Game = mongoose.model('game', gameSchema);

app.get('/games', async (req, res) => {
  try {
    const games = await Game.find();
    res.json(games);
  } catch (error) {
    console.error('Error fetching games:', error);
    res.status(500).send('Server Error');
  }
});

app.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
});