{"name": "app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"bootstrap": "^5.3.3", "cors": "^2.8.5", "express": "^5.1.0", "mongodb": "^6.16.0", "mongoose": "^8.14.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.14.2"}, "devDependencies": {"@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "@vitejs/plugin-react": "^4.0.0", "eslint": "^8.56.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "vite": "^4.4.9"}}