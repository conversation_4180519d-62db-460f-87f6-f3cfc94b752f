import {Navbar,Footer} from "./components/navbar";
import Games from "./components/games";
import "./App.css";
import { useState, useEffect } from "react";

function App() {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 2500);

    return () => clearTimeout(timer);
  }, []);

  return (
    <div className={`app ${isLoading ? "loading" : ""}`}>
      <Navbar />
      <Games />
      <Footer className="footer"/>
    </div>
  );
}

export default App;
